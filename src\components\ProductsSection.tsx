'use client'

import {
  Plus, Edit, Trash2, Search, Package, Filter, SortAsc, SortDesc,
  Grid, List, Download, AlertTriangle, CheckSquare, Square, Minus,
  X, ChevronDown, BarChart3, RefreshCw, ZoomIn, TrendingUp, Eye
} from 'lucide-react'
import Image from 'next/image'
import { useTheme } from 'next-themes'
import { useState, useEffect, useMemo, useCallback } from 'react'

import { Product, PRODUCT_CATEGORIES } from '@/lib/supabase'
import { exportProductsToCSV, exportProductsToJSON, exportAnalyticsReport } from '@/utils/exportUtils'

import LoadingSkeleton from './LoadingSkeleton'
import ProductImageZoom, { useImageZoom } from './ProductImageZoom'
import ProductModal from './ProductModal'
import ProductQuickActions from './ProductQuickActions'

interface ProductsSectionProps {
  onStatsUpdate: () => void
}

// Enhanced filter and sort types
type SortOption = 'name' | 'price' | 'stock' | 'category' | 'created_at'
type SortDirection = 'asc' | 'desc'
type ViewMode = 'grid' | 'list'

interface FilterOptions {
  category: string
  priceRange: { min: number; max: number }
  stockRange: { min: number; max: number }
  lowStock: boolean
  outOfStock: boolean
}

export default function ProductsSection({ onStatsUpdate }: ProductsSectionProps) {
  const { resolvedTheme } = useTheme()
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')

  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)

  // Advanced features state
  const [viewMode, setViewMode] = useState<ViewMode>('grid')
  const [sortBy, setSortBy] = useState<SortOption>('created_at')
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc')
  const [selectedProducts, setSelectedProducts] = useState<Set<string>>(new Set())
  const [showFilters, setShowFilters] = useState(false)
  const [filters, setFilters] = useState<FilterOptions>({
    category: '',
    priceRange: { min: 0, max: 10000 },
    stockRange: { min: 0, max: 1000 },
    lowStock: false,
    outOfStock: false
  })
  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)

  // Image zoom functionality
  const { zoomImage, openZoom, closeZoom, isZoomOpen } = useImageZoom()

  useEffect(() => {
    fetchProducts()
  }, [])

  // Advanced search suggestions
  const generateSearchSuggestions = useCallback((term: string) => {
    if (!term || term.length < 2) {
      setSearchSuggestions([])
      return
    }

    const suggestions = products
      .filter(product =>
        product.name.toLowerCase().includes(term.toLowerCase()) ||
        product.category.toLowerCase().includes(term.toLowerCase())
      )
      .map(product => product.name)
      .slice(0, 5)

    setSearchSuggestions([...new Set(suggestions)])
  }, [products])

  // Advanced filtering and sorting logic
  const filteredAndSortedProducts = useMemo(() => {
    const filtered = products.filter(product => {
      // Text search
      const matchesSearch = !searchTerm ||
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.category.toLowerCase().includes(searchTerm.toLowerCase())

      // Category filter
      const matchesCategory = !filters.category || product.category === filters.category

      // Price range filter
      const matchesPrice = product.price >= filters.priceRange.min &&
        product.price <= filters.priceRange.max

      // Stock range filter
      const matchesStock = product.stock_quantity >= filters.stockRange.min &&
        product.stock_quantity <= filters.stockRange.max

      // Low stock filter
      const matchesLowStock = !filters.lowStock || product.stock_quantity < 10

      // Out of stock filter
      const matchesOutOfStock = !filters.outOfStock || product.stock_quantity === 0

      return matchesSearch && matchesCategory && matchesPrice &&
        matchesStock && matchesLowStock && matchesOutOfStock
    })

    // Sorting
    filtered.sort((a, b) => {
      let aValue: string | number, bValue: string | number

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase()
          bValue = b.name.toLowerCase()
          break
        case 'price':
          aValue = a.price
          bValue = b.price
          break
        case 'stock':
          aValue = a.stock_quantity
          bValue = b.stock_quantity
          break
        case 'category':
          aValue = a.category.toLowerCase()
          bValue = b.category.toLowerCase()
          break
        case 'created_at':
          aValue = new Date(a.created_at).getTime()
          bValue = new Date(b.created_at).getTime()
          break
        default:
          return 0
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
      return 0
    })

    return filtered
  }, [products, searchTerm, filters, sortBy, sortDirection])

  const fetchProducts = async () => {
    try {
      // Using console.warn for informational messages (ESLint compliant)
      console.warn('🔄 Fetching products...')
      const response = await fetch('/api/products')

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      console.warn('📦 Products API response:', data)

      // Handle new API structure: { success: true, data: { products: [...] } }
      if (data.success && data.data && data.data.products) {
        setProducts(data.data.products)
        console.warn('✅ Products loaded (new structure):', data.data.products.length, 'items')
      }
      // Handle old API structure: { products: [...] }
      else if (data.products) {
        setProducts(data.products)
        console.warn('✅ Products loaded (old structure):', data.products.length, 'items')
      }
      // Handle direct array
      else if (Array.isArray(data)) {
        setProducts(data)
        console.warn('✅ Products loaded (direct array):', data.length, 'items')
      }
      else {
        console.warn('⚠️ Unexpected API response structure:', data)
        setProducts([])
      }
    } catch (error) {
      console.error('❌ Error fetching products:', error)
      setProducts([])
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this product?')) return

    try {
      const response = await fetch(`/api/products/${id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setProducts(products.filter(p => p.id !== id))
        onStatsUpdate()
      }
    } catch (error) {
      console.error('Error deleting product:', error)
    }
  }

  const handleEdit = (product: Product) => {
    setEditingProduct(product)
    setIsModalOpen(true)
  }

  const handleModalClose = () => {
    setIsModalOpen(false)
    setEditingProduct(null)
    fetchProducts()
    onStatsUpdate()
  }

  // Bulk operations handlers
  const handleSelectAll = () => {
    if (selectedProducts.size === filteredAndSortedProducts.length) {
      setSelectedProducts(new Set())
    } else {
      setSelectedProducts(new Set(filteredAndSortedProducts.map(p => p.id)))
    }
  }

  const handleSelectProduct = (productId: string) => {
    const newSelected = new Set(selectedProducts)
    if (newSelected.has(productId)) {
      newSelected.delete(productId)
    } else {
      newSelected.add(productId)
    }
    setSelectedProducts(newSelected)
  }

  const handleBulkDelete = async () => {
    if (selectedProducts.size === 0) return

    const confirmMessage = `Are you sure you want to delete ${selectedProducts.size} product(s)?`
    if (!confirm(confirmMessage)) return

    try {
      const deletePromises = Array.from(selectedProducts).map(id =>
        fetch(`/api/products/${id}`, { method: 'DELETE' })
      )

      await Promise.all(deletePromises)
      setSelectedProducts(new Set())
      fetchProducts()
      onStatsUpdate()
    } catch (error) {
      console.error('Error deleting products:', error)
    }
  }



  // Search handlers
  const handleSearchChange = (value: string) => {
    setSearchTerm(value)
    generateSearchSuggestions(value)
    setShowSuggestions(value.length >= 2)
  }

  const handleSuggestionClick = (suggestion: string) => {
    setSearchTerm(suggestion)
    setShowSuggestions(false)
  }



  // Export handlers
  const handleExportCSV = () => {
    exportProductsToCSV(filteredAndSortedProducts, `products_${new Date().toISOString().split('T')[0]}`)
  }

  const handleExportJSON = () => {
    exportProductsToJSON(filteredAndSortedProducts, `products_${new Date().toISOString().split('T')[0]}`)
  }

  const handleExportAnalytics = () => {
    exportAnalyticsReport(filteredAndSortedProducts, `product_analytics_${new Date().toISOString().split('T')[0]}`)
  }



  if (loading) {
    return (
      <div className="space-y-6">
        {/* Header Skeleton */}
        <div className="flex justify-between items-center">
          <div className="flex space-x-4">
            <div
              className="w-64 h-10 rounded-lg"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6',
                backgroundImage: resolvedTheme === 'dark'
                  ? 'linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%)'
                  : 'linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%)',
                backgroundSize: '200% 100%',
                animation: 'shimmer 2s infinite'
              }}
            />
            <div
              className="w-40 h-10 rounded-lg"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6',
                backgroundImage: resolvedTheme === 'dark'
                  ? 'linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%)'
                  : 'linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%)',
                backgroundSize: '200% 100%',
                animation: 'shimmer 2s infinite'
              }}
            />
          </div>
          <div
            className="w-48 h-10 rounded-lg"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6',
              backgroundImage: resolvedTheme === 'dark'
                ? 'linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%)'
                : 'linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%)',
              backgroundSize: '200% 100%',
              animation: 'shimmer 2s infinite'
            }}
          />
        </div>

        {/* Products Grid Skeleton */}
        <LoadingSkeleton type="products" count={8} />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Enhanced Header with Advanced Controls */}
      <div className="space-y-4">
        {/* Top Row - Search and Actions */}
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
          {/* Search Section */}
          <div className="flex flex-col sm:flex-row gap-3 flex-1">
            <div className="relative flex-1 max-w-md">
              <Search
                className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
                }}
              />
              <input
                type="text"
                placeholder="Search products by name or category..."
                value={searchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
                onFocus={() => setShowSuggestions(searchTerm.length >= 2)}
                onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
                className="w-full pl-10 pr-4 py-2.5 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 shadow-sm"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                  border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
              />

              {/* Search Suggestions */}
              {showSuggestions && searchSuggestions.length > 0 && (
                <div
                  className="absolute top-full left-0 right-0 mt-1 rounded-lg shadow-lg border z-50 max-h-48 overflow-y-auto"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                    border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db'
                  }}
                >
                  {searchSuggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      onClick={() => handleSuggestionClick(suggestion)}
                      className="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                      style={{
                        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                      }}
                    >
                      {suggestion}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Quick Filters */}
            <div className="flex gap-2">
              <button
                onClick={() => setFilters(prev => ({ ...prev, lowStock: !prev.lowStock }))}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                  filters.lowStock
                    ? 'bg-orange-100 text-orange-700 border-orange-300'
                    : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
                } border`}
              >
                <AlertTriangle className="h-4 w-4 inline mr-1" />
                Low Stock
              </button>
              <button
                onClick={() => setFilters(prev => ({ ...prev, outOfStock: !prev.outOfStock }))}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                  filters.outOfStock
                    ? 'bg-red-100 text-red-700 border-red-300'
                    : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
                } border`}
              >
                <X className="h-4 w-4 inline mr-1" />
                Out of Stock
              </button>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-2.5 rounded-xl font-medium transition-all duration-300 shadow-sm ${
                showFilters
                  ? 'bg-green-100 text-green-700 border-green-300'
                  : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
              } border`}
            >
              <Filter className="h-4 w-4 inline mr-2" />
              Filters
            </button>

            {/* Export Dropdown */}
            {filteredAndSortedProducts.length > 0 && (
              <div className="relative group">
                <button
                  className="px-4 py-2.5 rounded-xl font-medium transition-all duration-300 shadow-sm bg-blue-100 text-blue-700 border-blue-300 hover:bg-blue-200 border"
                >
                  <Download className="h-4 w-4 inline mr-2" />
                  Export
                  <ChevronDown className="h-4 w-4 inline ml-1" />
                </button>

                {/* Export Dropdown Menu */}
                <div className="absolute right-0 top-full mt-1 w-48 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                  <div
                    className="rounded-lg shadow-lg border py-1"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                      border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #e5e7eb'
                    }}
                  >
                    <button
                      onClick={handleExportCSV}
                      className="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-3"
                      style={{
                        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                      }}
                    >
                      <Download className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium">Export as CSV</span>
                    </button>
                    <button
                      onClick={handleExportJSON}
                      className="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-3"
                      style={{
                        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                      }}
                    >
                      <Download className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium">Export as JSON</span>
                    </button>
                    <div
                      className="my-1 h-px"
                      style={{
                        backgroundColor: resolvedTheme === 'dark' ? '#6b7280' : '#e5e7eb'
                      }}
                    />
                    <button
                      onClick={handleExportAnalytics}
                      className="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-3"
                      style={{
                        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                      }}
                    >
                      <BarChart3 className="h-4 w-4 text-purple-600" />
                      <span className="text-sm font-medium">Analytics Report</span>
                    </button>
                  </div>
                </div>
              </div>
            )}

            <button
              onClick={() => setIsModalOpen(true)}
              className="flex items-center px-6 py-2.5 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg font-medium"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Product
            </button>
          </div>
        </div>

        {/* Advanced Filters Panel */}
        {showFilters && (
          <div
            className="p-6 rounded-xl border shadow-sm animate-slide-down"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f8fafc',
              border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #e2e8f0'
            }}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Category Filter */}
              <div>
                <label className="block text-sm font-medium mb-2" style={{
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}>
                  Category
                </label>
                <select
                  value={filters.category}
                  onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
                  className="w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                    border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                  }}
                >
                  <option value="">All Categories</option>
                  {PRODUCT_CATEGORIES.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>

              {/* Price Range */}
              <div>
                <label className="block text-sm font-medium mb-2" style={{
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}>
                  Price Range (₱)
                </label>
                <div className="flex gap-2">
                  <input
                    type="number"
                    placeholder="Min"
                    value={filters.priceRange.min}
                    onChange={(e) => setFilters(prev => ({
                      ...prev,
                      priceRange: { ...prev.priceRange, min: Number(e.target.value) || 0 }
                    }))}
                    className="w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                      border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                      color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                    }}
                  />
                  <input
                    type="number"
                    placeholder="Max"
                    value={filters.priceRange.max}
                    onChange={(e) => setFilters(prev => ({
                      ...prev,
                      priceRange: { ...prev.priceRange, max: Number(e.target.value) || 10000 }
                    }))}
                    className="w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                      border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                      color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                    }}
                  />
                </div>
              </div>

              {/* Stock Range */}
              <div>
                <label className="block text-sm font-medium mb-2" style={{
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}>
                  Stock Range
                </label>
                <div className="flex gap-2">
                  <input
                    type="number"
                    placeholder="Min"
                    value={filters.stockRange.min}
                    onChange={(e) => setFilters(prev => ({
                      ...prev,
                      stockRange: { ...prev.stockRange, min: Number(e.target.value) || 0 }
                    }))}
                    className="w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                      border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                      color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                    }}
                  />
                  <input
                    type="number"
                    placeholder="Max"
                    value={filters.stockRange.max}
                    onChange={(e) => setFilters(prev => ({
                      ...prev,
                      stockRange: { ...prev.stockRange, max: Number(e.target.value) || 1000 }
                    }))}
                    className="w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                      border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                      color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                    }}
                  />
                </div>
              </div>

              {/* Reset Filters */}
              <div className="flex items-end">
                <button
                  onClick={() => setFilters({
                    category: '',
                    priceRange: { min: 0, max: 10000 },
                    stockRange: { min: 0, max: 1000 },
                    lowStock: false,
                    outOfStock: false
                  })}
                  className="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-all duration-300 font-medium"
                >
                  <RefreshCw className="h-4 w-4 inline mr-2" />
                  Reset Filters
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Toolbar - Sort, View Mode, Bulk Actions */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          {/* Left Side - Results Info & Bulk Actions */}
          <div className="flex items-center gap-4">
            <span className="text-sm font-medium" style={{
              color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
            }}>
              {filteredAndSortedProducts.length} product{filteredAndSortedProducts.length !== 1 ? 's' : ''} found
            </span>

            {/* Bulk Selection */}
            {filteredAndSortedProducts.length > 0 && (
              <div className="flex items-center gap-2">
                <button
                  onClick={handleSelectAll}
                  className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  title={selectedProducts.size === filteredAndSortedProducts.length ? 'Deselect All' : 'Select All'}
                >
                  {selectedProducts.size === filteredAndSortedProducts.length ? (
                    <CheckSquare className="h-4 w-4 text-green-600" />
                  ) : selectedProducts.size > 0 ? (
                    <Minus className="h-4 w-4 text-gray-600" />
                  ) : (
                    <Square className="h-4 w-4 text-gray-600" />
                  )}
                </button>

                {selectedProducts.size > 0 && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-green-600 font-medium">
                      {selectedProducts.size} selected
                    </span>
                    <button
                      onClick={handleBulkDelete}
                      className="px-3 py-1 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors text-sm font-medium"
                    >
                      <Trash2 className="h-3 w-3 inline mr-1" />
                      Delete
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Right Side - Sort & View Controls */}
          <div className="flex items-center gap-3">
            {/* Sort Controls */}
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium" style={{
                color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
              }}>
                Sort by:
              </span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as SortOption)}
                className="px-3 py-1.5 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                  border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
              >
                <option value="created_at">Date Added</option>
                <option value="name">Name</option>
                <option value="price">Price</option>
                <option value="stock">Stock</option>
                <option value="category">Category</option>
              </select>
              <button
                onClick={() => setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')}
                className="p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                title={`Sort ${sortDirection === 'asc' ? 'Descending' : 'Ascending'}`}
              >
                {sortDirection === 'asc' ? (
                  <SortAsc className="h-4 w-4" style={{
                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                  }} />
                ) : (
                  <SortDesc className="h-4 w-4" style={{
                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                  }} />
                )}
              </button>
            </div>

            {/* View Mode Toggle */}
            <div className="flex rounded-lg border" style={{
              border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db'
            }}>
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-green-100 text-green-700'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
                title="Grid View"
              >
                <Grid className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 transition-colors ${
                  viewMode === 'list'
                    ? 'bg-green-100 text-green-700'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
                title="List View"
              >
                <List className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Products Grid/List */}
      <div className={viewMode === 'grid'
        ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
        : "space-y-4"
      }>
        {filteredAndSortedProducts.map((product) => (
          <div
            key={product.id}
            className={`relative group rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-xl ${
              selectedProducts.has(product.id)
                ? 'ring-2 ring-green-500 ring-offset-2'
                : 'hover:scale-[1.02]'
            } ${viewMode === 'list' ? 'flex items-center' : ''}`}
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
              border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'
            }}
          >
            {/* Selection Checkbox */}
            <div className="absolute top-3 left-3 z-10">
              <button
                onClick={() => handleSelectProduct(product.id)}
                className={`p-1 rounded-md transition-all duration-200 ${
                  selectedProducts.has(product.id)
                    ? 'bg-green-500 text-white'
                    : 'bg-white/80 text-gray-600 hover:bg-white'
                } shadow-sm`}
              >
                {selectedProducts.has(product.id) ? (
                  <CheckSquare className="h-4 w-4" />
                ) : (
                  <Square className="h-4 w-4" />
                )}
              </button>
            </div>

            {/* Quick Actions Menu */}
            <div className="absolute top-3 right-3 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <ProductQuickActions
                product={product}
                onEdit={handleEdit}
                onDelete={handleDelete}
                onDuplicate={(product) => {
                  // Create a duplicate product
                  setEditingProduct({
                    ...product,
                    id: '',
                    name: `${product.name} (Copy)`,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                  })
                  setIsModalOpen(true)
                }}
              />
            </div>

            {/* Product Image */}
            <div
              className={`${viewMode === 'grid' ? 'aspect-square' : 'w-24 h-24 flex-shrink-0'} flex items-center justify-center transition-colors duration-300 relative overflow-hidden cursor-pointer group/image`}
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6'
              }}
              onClick={() => openZoom(product.image_url, product.name)}
            >
              {product.image_url ? (
                <>
                  <Image
                    src={product.image_url}
                    alt={product.name}
                    width={viewMode === 'grid' ? 200 : 96}
                    height={viewMode === 'grid' ? 200 : 96}
                    className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  {/* Zoom Overlay */}
                  <div className="absolute inset-0 bg-black/20 opacity-0 group-hover/image:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                    <div className="bg-white/90 rounded-full p-2">
                      <ZoomIn className="h-4 w-4 text-gray-700" />
                    </div>
                  </div>
                </>
              ) : (
                <Package
                  className={`${viewMode === 'grid' ? 'h-16 w-16' : 'h-12 w-12'} transition-colors duration-300`}
                  style={{
                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
                  }}
                />
              )}

              {/* Stock Status Overlay */}
              {product.stock_quantity === 0 && (
                <div className="absolute inset-0 bg-red-500/20 flex items-center justify-center">
                  <span className="bg-red-500 text-white px-2 py-1 rounded-md text-xs font-medium">
                    Out of Stock
                  </span>
                </div>
              )}
              {product.stock_quantity > 0 && product.stock_quantity < 10 && (
                <div className="absolute top-2 right-2">
                  <span className="bg-orange-500 text-white px-2 py-1 rounded-md text-xs font-medium">
                    Low Stock
                  </span>
                </div>
              )}
            </div>

            {/* Product Info */}
            <div className={`${viewMode === 'grid' ? 'p-4' : 'flex-1 p-4'}`}>
              <div className={`${viewMode === 'list' ? 'flex items-center justify-between' : ''}`}>
                <div className={`${viewMode === 'list' ? 'flex-1' : ''}`}>
                  <h3
                    className="font-semibold mb-1 transition-colors duration-300 line-clamp-2"
                    style={{
                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                    }}
                  >
                    {product.name}
                  </h3>

                  <div className={`${viewMode === 'list' ? 'flex items-center gap-4' : 'space-y-2'}`}>
                    <p
                      className="text-sm transition-colors duration-300"
                      style={{
                        color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                      }}
                    >
                      {product.category}
                    </p>

                    <div className={`${viewMode === 'list' ? 'flex items-center gap-4' : 'flex justify-between items-center'}`}>
                      <span className="text-lg font-bold text-green-600">₱{product.price.toFixed(2)}</span>
                      <span
                        className="text-sm transition-colors duration-300"
                        style={{
                          color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
                        }}
                      >
                        {product.net_weight}
                      </span>
                    </div>

                    <div className={`${viewMode === 'list' ? 'flex items-center gap-2' : 'flex justify-between items-center mb-4'}`}>
                      <span
                        className={`text-sm ${
                          product.stock_quantity === 0
                            ? 'text-red-600 font-medium'
                            : product.stock_quantity < 10
                              ? 'text-orange-600 font-medium'
                              : ''
                        }`}
                        style={{
                          color: product.stock_quantity >= 10
                            ? (resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280')
                            : undefined
                        }}
                      >
                        Stock: {product.stock_quantity}
                      </span>

                      {viewMode === 'grid' && (
                        <div className="flex items-center gap-1">
                          {product.stock_quantity < 10 && (
                            <AlertTriangle className="h-4 w-4 text-orange-500" />
                          )}
                          <TrendingUp className="h-4 w-4 text-green-500" />
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className={`${viewMode === 'list' ? 'flex gap-2 ml-4' : 'flex gap-2'}`}>
                  <button
                    onClick={() => handleEdit(product)}
                    className="flex-1 flex items-center justify-center px-3 py-2 rounded-lg transition-all duration-300 hover:scale-105 font-medium"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#1e40af' : '#dbeafe',
                      color: resolvedTheme === 'dark' ? '#ffffff' : '#1e40af',
                      border: resolvedTheme === 'dark' ? '1px solid #3b82f6' : '1px solid #93c5fd'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? '#1d4ed8' : '#bfdbfe'
                      e.currentTarget.style.transform = 'scale(1.05)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? '#1e40af' : '#dbeafe'
                      e.currentTarget.style.transform = 'scale(1)'
                    }}
                    title="Edit Product"
                  >
                    <Edit className="h-4 w-4 mr-1" />
                    {viewMode === 'grid' ? 'Edit' : ''}
                  </button>
                  <button
                    onClick={() => handleDelete(product.id)}
                    className="flex-1 flex items-center justify-center px-3 py-2 rounded-lg transition-all duration-300 hover:scale-105 font-medium"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#dc2626' : '#fecaca',
                      color: resolvedTheme === 'dark' ? '#ffffff' : '#dc2626',
                      border: resolvedTheme === 'dark' ? '1px solid #ef4444' : '1px solid #fca5a5'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? '#b91c1c' : '#fca5a5'
                      e.currentTarget.style.transform = 'scale(1.05)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? '#dc2626' : '#fecaca'
                      e.currentTarget.style.transform = 'scale(1)'
                    }}
                    title="Delete Product"
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    {viewMode === 'grid' ? 'Delete' : ''}
                  </button>
                  {viewMode === 'list' && (
                    <button
                      className="px-3 py-2 rounded-lg transition-all duration-300 hover:scale-105 font-medium"
                      style={{
                        backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6',
                        color: resolvedTheme === 'dark' ? '#d1d5db' : '#374151',
                        border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb'
                        e.currentTarget.style.transform = 'scale(1.05)'
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? '#374151' : '#f3f4f6'
                        e.currentTarget.style.transform = 'scale(1)'
                      }}
                      title="View Details"
                    >
                      <Eye className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredAndSortedProducts.length === 0 && !loading && (
        <div
          className="text-center py-16 rounded-xl border-2 border-dashed transition-all duration-300"
          style={{
            backgroundColor: resolvedTheme === 'dark' ? 'rgba(30, 41, 59, 0.5)' : 'rgba(249, 250, 251, 0.8)',
            borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'
          }}
        >
          <div
            className="w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 transition-all duration-300"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(34, 197, 94, 0.05)',
              border: resolvedTheme === 'dark' ? '2px solid rgba(34, 197, 94, 0.3)' : '2px solid rgba(34, 197, 94, 0.2)'
            }}
          >
            <Package
              className="h-10 w-10 transition-colors duration-300"
              style={{
                color: resolvedTheme === 'dark' ? '#4ade80' : '#16a34a'
              }}
            />
          </div>
          <h3
            className="text-xl font-semibold mb-3 transition-colors duration-300"
            style={{
              color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
            }}
          >
            {searchTerm || filters.category || filters.lowStock || filters.outOfStock
              ? 'No products found'
              : 'No products in your inventory'}
          </h3>
          <p
            className="text-sm mb-6 max-w-md mx-auto transition-colors duration-300"
            style={{
              color: resolvedTheme === 'dark' ? '#94a3b8' : '#6b7280'
            }}
          >
            {searchTerm || filters.category || filters.lowStock || filters.outOfStock
              ? 'Try adjusting your search terms or filter criteria to find what you\'re looking for'
              : 'Get started by adding your first product to build your inventory and start managing your store'}
          </p>
          {!searchTerm && !filters.category && !filters.lowStock && !filters.outOfStock && (
            <button
              onClick={() => setIsModalOpen(true)}
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-lg hover:shadow-xl"
            >
              <Plus className="h-5 w-5 mr-2" />
              Add Your First Product
            </button>
          )}
        </div>
      )}

      {/* Product Modal */}
      <ProductModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        product={editingProduct}
      />

      {/* Image Zoom Modal */}
      <ProductImageZoom
        imageUrl={zoomImage?.imageUrl}
        productName={zoomImage?.productName || ''}
        isOpen={isZoomOpen}
        onClose={closeZoom}
      />
    </div>
  )
}
